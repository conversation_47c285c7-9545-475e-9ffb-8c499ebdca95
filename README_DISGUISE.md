# 伪装模式使用指南

## 概述

伪装模式是一个特殊功能，可以将所有金融相关术语替换为频率/信号处理术语，有效隐藏量化交易的真实目的。当启用伪装模式时，图表和输出中的所有术语都会被替换，使整个系统看起来像是在进行频率响应分析而不是股票交易分析。

## 术语映射表

| 金融术语 | 伪装术语 | 说明 |
|---------|---------|------|
| 价格 | 频率 | 股票价格 → 信号频率 |
| 收盘价 | 采样值 | 收盘价 → 采样点数值 |
| 开盘价 | 初始值 | 开盘价 → 初始频率 |
| 最高价 | 峰值 | 最高价 → 频率峰值 |
| 最低价 | 谷值 | 最低价 → 频率谷值 |
| 成交量 | 数据量 | 交易量 → 数据采样量 |
| 买入信号 | 增强响应 | 买入 → 信号增强 |
| 卖出信号 | 衰减响应 | 卖出 → 信号衰减 |
| MACD | 频差 | MACD指标 → 频率差分 |
| Signal线 | 基准线 | 信号线 → 基准响应 |
| 直方图 | 差分谱 | MACD直方图 → 频差谱 |
| 总收益率 | 总增益 | 投资收益 → 系统增益 |
| 夏普比率 | 信噪比 | 风险调整收益 → 信号质量 |
| 最大回撤 | 最大衰减 | 最大损失 → 最大信号衰减 |
| 胜率 | 有效率 | 盈利交易比例 → 有效响应率 |
| $ | Hz | 货币单位 → 频率单位 |
| % | dB | 百分比 → 分贝 |

## 使用方法

### 方法1：直接在代码中设置

```python
from lB_BT_Plotly import BacktestSystem

# 启用伪装模式
system = BacktestSystem(disguise_mode=True)

# 禁用伪装模式（默认）
system = BacktestSystem(disguise_mode=False)
```

### 方法2：使用配置文件

1. 编辑 `config.py` 文件：
```python
# 设置为 True 启用伪装模式
DISGUISE_MODE = True
```

2. 使用配置运行：
```python
from config import get_backtest_config
from lB_BT_Plotly import BacktestSystem

config = get_backtest_config()
system = BacktestSystem(**config)
```

### 方法3：使用配置脚本

直接运行配置脚本：
```bash
python run_backtest_with_config.py
```

## 效果对比

### 正常模式输出示例：
```
股票代码: AAPL.US
初始资金: $100,000.00
总收益率: 15.23%
夏普比率: 1.2345
最大回撤: -8.45%
买入信号、卖出信号
MACD指标、Signal线
```

### 伪装模式输出示例：
```
信号代码: AAPL.US
初始功率: 100,000.00Hz
总增益: 15.23dB
信噪比: 1.2345
最大衰减: -8.45dB
增强响应、衰减响应
频差指标、基准线
```

## 图表变化

启用伪装模式后，图表中的所有标签、标题和说明都会相应改变：

- 图表标题：`AAPL.US MACD策略回测结果` → `AAPL.US 频差滤波器频响分析结果`
- Y轴标签：`价格 ($)` → `频率 (Hz)`
- 图例：`买入信号` → `增强响应`，`卖出信号` → `衰减响应`
- 悬停提示：所有金融术语都被替换为对应的技术术语

## 演示功能

### 1. 基本演示
```python
# 在 lB_BT_Plotly.py 中取消注释
demo_disguise_mode()
```

### 2. 对比演示
```python
# 运行配置脚本中的对比功能
python run_backtest_with_config.py
```

### 3. 切换演示
```python
from config import DISGUISE_MODE
# 修改配置并重新运行
```

## 注意事项

1. **数据一致性**：伪装模式只改变显示术语，不影响实际计算逻辑
2. **配置持久性**：配置文件中的设置会影响所有使用该配置的实例
3. **图表兼容性**：所有图表功能在伪装模式下都正常工作
4. **术语完整性**：确保所有相关术语都已在映射表中定义

## 自定义术语

可以在 `config.py` 中的 `DISGUISE_TERMS` 字典中添加或修改术语映射：

```python
DISGUISE_TERMS = {
    '你的金融术语': '你的伪装术语',
    # 添加更多映射...
}
```

## 应用场景

1. **学术演示**：在学术环境中演示信号处理算法
2. **技术展示**：向非金融背景人员展示技术分析方法
3. **隐私保护**：在公共场合或共享屏幕时隐藏交易意图
4. **教学用途**：将金融概念类比为信号处理概念进行教学

## 技术实现

伪装功能通过以下方式实现：
- 在绘图器类中添加术语替换逻辑
- 在图表生成时动态替换所有文本内容
- 在结果输出时应用术语映射
- 保持所有数值计算的准确性
