"""
实盘MACD交易策略
================

基于MACD直方图差分信号的实盘交易策略实现。
包含MACD计算、信号滤波、交易信号生成等核心功能。
"""

import pandas as pd
import numpy as np
from scipy import signal
from datetime import datetime, timedelta
import logging
from typing import Tuple, Optional, List
from live_trading_config import get_trading_config

class LiveMACDStrategy:
    """
    实盘MACD交易策略
    ================
    
    基于MACD直方图差分的交易策略，包含以下功能：
    1. 实时计算MACD指标
    2. 对直方图进行滤波处理
    3. 计算差分信号并生成交易信号
    4. 提供风险控制和信号确认
    """
    
    def __init__(self, config=None):
        """
        初始化策略
        
        Args:
            config (dict, optional): 策略配置，如果不提供则使用默认配置
        """
        self.config = config or get_trading_config()
        self.macd_params = self.config['macd_params']
        self.filter_params = self.config['filter_params']
        self.signal_params = self.config['signal_params']
        self.risk_params = self.config['risk_params']
        
        # 初始化数据存储
        self.price_data = pd.DataFrame()
        self.macd_data = pd.DataFrame()
        self.histogram_history = []
        self.filtered_histogram_history = []
        self.signal_history = []
        
        # 交易状态
        self.current_position = 0
        self.last_trade_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        # 设置日志
        self.logger = self._setup_logger()
        
        self.logger.info("实盘MACD策略初始化完成")
        self.logger.info(f"MACD参数: {self.macd_params}")
        self.logger.info(f"滤波参数: {self.filter_params}")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('LiveMACDStrategy')
        logger.setLevel(getattr(logging, self.config['logging_config']['log_level']))
        
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(
                f"strategy_{self.config['logging_config']['log_file']}"
            )
            file_handler.setLevel(logging.DEBUG)
            
            # 控制台处理器
            if self.config['logging_config']['enable_console_log']:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                
                # 格式化器
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                file_handler.setFormatter(formatter)
                console_handler.setFormatter(formatter)
                
                logger.addHandler(file_handler)
                logger.addHandler(console_handler)
        
        return logger
    
    def update_data(self, new_data: pd.DataFrame):
        """
        更新价格数据并计算指标
        
        Args:
            new_data (pd.DataFrame): 新的价格数据，包含OHLCV列
        """
        try:
            # 更新价格数据
            if self.price_data.empty:
                self.price_data = new_data.copy()
            else:
                # 合并新数据，避免重复
                self.price_data = pd.concat([self.price_data, new_data]).drop_duplicates()
                self.price_data = self.price_data.sort_index()
            
            # 保持数据长度合理（最近200个数据点）
            if len(self.price_data) > 200:
                self.price_data = self.price_data.tail(200)
            
            # 计算MACD指标
            self._calculate_macd()
            
            # 更新直方图历史
            if not self.macd_data.empty:
                latest_histogram = self.macd_data['histogram'].iloc[-1]
                self.histogram_history.append(latest_histogram)
                
                # 保持历史长度合理
                if len(self.histogram_history) > 100:
                    self.histogram_history = self.histogram_history[-100:]
            
            self.logger.debug(f"数据更新完成，当前数据长度: {len(self.price_data)}")
            
        except Exception as e:
            self.logger.error(f"数据更新失败: {e}")
            raise
    
    def _calculate_macd(self):
        """计算MACD指标"""
        if len(self.price_data) < self.macd_params['slow_period']:
            self.logger.warning("数据不足，无法计算MACD")
            return
        
        try:
            # 计算EMA
            close_prices = self.price_data['close']
            ema_fast = close_prices.ewm(span=self.macd_params['fast_period']).mean()
            ema_slow = close_prices.ewm(span=self.macd_params['slow_period']).mean()
            
            # 计算MACD线
            macd_line = ema_fast - ema_slow
            
            # 计算信号线
            signal_line = macd_line.ewm(span=self.macd_params['signal_period']).mean()
            
            # 计算直方图
            histogram = macd_line - signal_line
            
            # 存储MACD数据
            self.macd_data = pd.DataFrame({
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }, index=self.price_data.index)
            
            self.logger.debug("MACD指标计算完成")
            
        except Exception as e:
            self.logger.error(f"MACD计算失败: {e}")
            raise
    
    def _apply_filter(self, data: List[float]) -> List[float]:
        """
        对数据应用滤波器
        
        Args:
            data (List[float]): 原始数据
            
        Returns:
            List[float]: 滤波后的数据
        """
        if len(data) < 3:
            return data
        
        try:
            filter_type = self.filter_params['filter_type']
            
            if filter_type == 'savgol':
                # Savitzky-Golay滤波器
                window_length = min(self.filter_params['window_length'], len(data))
                if window_length % 2 == 0:
                    window_length -= 1
                
                polyorder = min(self.filter_params['polyorder'], window_length - 1)
                
                if window_length >= 3:
                    filtered_data = signal.savgol_filter(data, window_length, polyorder)
                    return filtered_data.tolist()
                else:
                    return data
                    
            elif filter_type == 'ema':
                # 指数移动平均滤波
                alpha = self.filter_params['ema_alpha']
                filtered_data = [data[0]]
                for i in range(1, len(data)):
                    filtered_value = alpha * data[i] + (1 - alpha) * filtered_data[-1]
                    filtered_data.append(filtered_value)
                return filtered_data
                
            elif filter_type == 'sma':
                # 简单移动平均滤波
                window = self.filter_params['sma_window']
                filtered_data = []
                for i in range(len(data)):
                    start_idx = max(0, i - window + 1)
                    window_data = data[start_idx:i+1]
                    filtered_data.append(np.mean(window_data))
                return filtered_data
                
            else:
                self.logger.warning(f"未知的滤波器类型: {filter_type}")
                return data
                
        except Exception as e:
            self.logger.error(f"滤波处理失败: {e}")
            return data
    
    def generate_signal(self) -> int:
        """
        生成交易信号
        
        Returns:
            int: 交易信号 (1: 买入, -1: 卖出, 0: 无信号)
        """
        if len(self.histogram_history) < 3:
            return 0
        
        try:
            # 对直方图进行滤波
            filtered_histogram = self._apply_filter(self.histogram_history)
            self.filtered_histogram_history = filtered_histogram
            
            # 计算差分
            histogram_diff = np.diff(filtered_histogram)
            
            if len(histogram_diff) < 2:
                return 0
            
            # 检查差分符号变化
            current_diff = histogram_diff[-1]
            previous_diff = histogram_diff[-2]
            
            # 最小阈值过滤
            min_threshold = self.signal_params['min_histogram_threshold']
            
            signal = 0
            
            # 买入信号：差分从负变正（上升趋势开始）
            if (previous_diff <= 0 and current_diff > 0 and 
                abs(current_diff) > min_threshold):
                signal = 1
                self.logger.info(f"生成买入信号: 差分从 {previous_diff:.6f} 变为 {current_diff:.6f}")
            
            # 卖出信号：差分从正变负（下降趋势开始）
            elif (previous_diff >= 0 and current_diff < 0 and 
                  abs(current_diff) > min_threshold):
                signal = -1
                self.logger.info(f"生成卖出信号: 差分从 {previous_diff:.6f} 变为 {current_diff:.6f}")
            
            # 记录信号历史
            self.signal_history.append(signal)
            if len(self.signal_history) > 50:
                self.signal_history = self.signal_history[-50:]
            
            return signal
            
        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return 0
    
    def can_trade(self, signal: int) -> Tuple[bool, str]:
        """
        检查是否可以交易
        
        Args:
            signal (int): 交易信号
            
        Returns:
            Tuple[bool, str]: (是否可以交易, 原因)
        """
        current_time = datetime.now()
        
        # 检查交易时间
        trading_start = datetime.strptime(
            self.risk_params['trading_start_time'], "%H:%M"
        ).time()
        trading_end = datetime.strptime(
            self.risk_params['trading_end_time'], "%H:%M"
        ).time()
        
        if not (trading_start <= current_time.time() <= trading_end):
            return False, "不在交易时间内"
        
        # 检查每日交易次数
        if self.last_trade_date != current_time.date():
            self.daily_trade_count = 0
            self.last_trade_date = current_time.date()
        
        if self.daily_trade_count >= self.risk_params['max_daily_trades']:
            return False, "已达到每日最大交易次数"
        
        # 检查持仓限制
        if signal == 1:  # 买入信号
            if self.current_position >= self.risk_params['max_position']:
                return False, "已达到最大持仓限制"
        elif signal == -1:  # 卖出信号
            if self.current_position <= 0:
                return False, "当前无持仓"
        
        return True, "可以交易"
    
    def get_strategy_status(self) -> dict:
        """
        获取策略状态信息
        
        Returns:
            dict: 策略状态信息
        """
        return {
            'current_position': self.current_position,
            'daily_trade_count': self.daily_trade_count,
            'last_trade_time': self.last_trade_time,
            'data_length': len(self.price_data),
            'histogram_length': len(self.histogram_history),
            'latest_histogram': self.histogram_history[-1] if self.histogram_history else None,
            'latest_signal': self.signal_history[-1] if self.signal_history else 0,
        }
