"""
实盘交易配置文件
================

用于配置实盘交易系统的各种参数，包括交易标的、策略参数、风险控制等。
"""

from longport.openapi import Period

# ==================== 基本配置 ====================

# 交易标的配置
TRADING_SYMBOL = "AAPL.US"  # 交易标的，可以修改为其他股票
TRADING_PERIOD = Period.Min_60  # 1小时K线数据
POSITION_SIZE = 1  # 每次交易数量（手）

# 数据获取配置
DATA_LOOKBACK_DAYS = 30  # 获取多少天的历史数据用于计算指标
DATA_UPDATE_INTERVAL = 60  # 数据更新间隔（秒）

# ==================== MACD策略参数 ====================

MACD_PARAMS = {
    'fast_period': 12,      # MACD快线周期
    'slow_period': 26,      # MACD慢线周期
    'signal_period': 9,     # MACD信号线周期
}

# ==================== 滤波器参数 ====================

FILTER_PARAMS = {
    'filter_type': 'savgol',    # 滤波器类型：'savgol', 'ema', 'sma'
    'window_length': 11,        # Savitzky-Golay滤波器窗口长度
    'polyorder': 2,             # Savitzky-Golay滤波器多项式阶数
    'ema_alpha': 0.2,          # EMA滤波器平滑因子
    'sma_window': 5,           # 简单移动平均窗口
}

# ==================== 交易信号参数 ====================

SIGNAL_PARAMS = {
    'min_histogram_threshold': 0.001,  # 最小直方图阈值，避免噪音交易
    'signal_confirmation_bars': 1,     # 信号确认需要的K线数量
    'enable_signal_filter': True,      # 是否启用信号过滤
}

# ==================== 风险控制参数 ====================

RISK_PARAMS = {
    'max_position': 10,         # 最大持仓数量（手）
    'stop_loss_pct': 0.05,      # 止损百分比（5%）
    'take_profit_pct': 0.10,    # 止盈百分比（10%）
    'max_daily_trades': 10,     # 每日最大交易次数
    'trading_start_time': "09:30",  # 交易开始时间
    'trading_end_time': "16:00",    # 交易结束时间
}

# ==================== 日志和监控配置 ====================

LOGGING_CONFIG = {
    'log_level': 'INFO',        # 日志级别：DEBUG, INFO, WARNING, ERROR
    'log_file': 'live_trading.log',  # 日志文件名
    'enable_console_log': True,  # 是否在控制台输出日志
    'enable_trade_log': True,   # 是否记录交易日志
}

# ==================== 数据缓存配置 ====================

CACHE_CONFIG = {
    'enable_cache': True,       # 是否启用数据缓存
    'cache_dir': 'live_data_cache',  # 缓存目录
    'cache_expiry_hours': 1,    # 缓存过期时间（小时）
}

# ==================== 实盘交易模式配置 ====================

TRADING_MODE = {
    'mode': 'paper',  # 交易模式：'paper'（模拟）, 'live'（实盘）
    'paper_initial_cash': 100000,  # 模拟交易初始资金
    'enable_order_confirmation': True,  # 是否需要订单确认
}

# ==================== 监控和报警配置 ====================

MONITORING_CONFIG = {
    'enable_email_alerts': False,   # 是否启用邮件报警
    'email_recipients': [],         # 邮件接收者列表
    'alert_on_trade': True,         # 交易时是否报警
    'alert_on_error': True,         # 错误时是否报警
    'performance_report_interval': 3600,  # 性能报告间隔（秒）
}

# ==================== 辅助函数 ====================

def get_trading_config():
    """
    获取完整的交易配置
    
    Returns:
        dict: 包含所有配置的字典
    """
    return {
        'symbol': TRADING_SYMBOL,
        'period': TRADING_PERIOD,
        'position_size': POSITION_SIZE,
        'data_config': {
            'lookback_days': DATA_LOOKBACK_DAYS,
            'update_interval': DATA_UPDATE_INTERVAL,
        },
        'macd_params': MACD_PARAMS,
        'filter_params': FILTER_PARAMS,
        'signal_params': SIGNAL_PARAMS,
        'risk_params': RISK_PARAMS,
        'logging_config': LOGGING_CONFIG,
        'cache_config': CACHE_CONFIG,
        'trading_mode': TRADING_MODE,
        'monitoring_config': MONITORING_CONFIG,
    }

def validate_config():
    """
    验证配置参数的有效性
    
    Returns:
        tuple: (is_valid, error_messages)
    """
    errors = []
    
    # 验证MACD参数
    if MACD_PARAMS['fast_period'] >= MACD_PARAMS['slow_period']:
        errors.append("MACD快线周期必须小于慢线周期")
    
    # 验证滤波器参数
    if FILTER_PARAMS['window_length'] < 3:
        errors.append("滤波器窗口长度必须至少为3")
    
    if FILTER_PARAMS['polyorder'] >= FILTER_PARAMS['window_length']:
        errors.append("多项式阶数必须小于窗口长度")
    
    # 验证风险参数
    if RISK_PARAMS['stop_loss_pct'] <= 0 or RISK_PARAMS['stop_loss_pct'] >= 1:
        errors.append("止损百分比必须在0-1之间")
    
    if RISK_PARAMS['take_profit_pct'] <= 0:
        errors.append("止盈百分比必须大于0")
    
    # 验证交易时间
    try:
        from datetime import datetime
        datetime.strptime(RISK_PARAMS['trading_start_time'], "%H:%M")
        datetime.strptime(RISK_PARAMS['trading_end_time'], "%H:%M")
    except ValueError:
        errors.append("交易时间格式错误，应为HH:MM格式")
    
    return len(errors) == 0, errors

# 使用示例
if __name__ == "__main__":
    # 验证配置
    is_valid, errors = validate_config()
    if is_valid:
        print("✓ 配置验证通过")
        config = get_trading_config()
        print(f"交易标的: {config['symbol']}")
        print(f"交易模式: {config['trading_mode']['mode']}")
    else:
        print("✗ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
