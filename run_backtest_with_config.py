"""
使用配置文件运行回测的示例脚本
==============================

这个脚本演示如何使用配置文件来管理回测系统的各种设置，
包括伪装模式的开启和关闭。
"""

from datetime import datetime
from lB_BT_Plotly import BacktestSystem
from longport.openapi import Period
import config

def run_backtest_with_config():
    """
    使用配置文件运行回测
    """
    print("="*70)
    print("使用配置文件运行回测系统")
    print("="*70)
    
    # 从配置文件获取设置
    backtest_config = config.get_backtest_config()
    chart_config = config.get_chart_config()
    
    print(f"当前配置:")
    print(f"- 伪装模式: {'启用' if backtest_config['disguise_mode'] else '禁用'}")
    print(f"- 缓存功能: {'启用' if backtest_config['enable_cache'] else '禁用'}")
    print(f"- 初始资金: ${backtest_config['initial_cash']:,}")
    print(f"- 手续费率: {backtest_config['commission']*100:.1f}%")
    print("="*70)
    
    # 创建回测系统
    system = BacktestSystem(
        enable_cache=backtest_config['enable_cache'],
        cache_dir=backtest_config['cache_dir'],
        disguise_mode=backtest_config['disguise_mode']
    )
    
    # 回测参数
    symbol = "YINN.US"
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 7, 24)
    
    # 运行回测
    results = system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=backtest_config['initial_cash']
    )
    
    # 显示结果
    if results:
        fig = system.plot_results(symbol)
        if fig:
            fig.show()
            print(f"\n回测完成！")
            if backtest_config['disguise_mode']:
                print("注意：当前使用伪装模式，所有术语已替换为频率/信号处理术语")
            else:
                print("当前使用正常模式，显示标准金融术语")
        else:
            print("图表生成失败")
    else:
        print("回测失败")

def toggle_disguise_mode():
    """
    切换伪装模式的示例函数
    """
    print("\n" + "="*50)
    print("伪装模式切换演示")
    print("="*50)
    
    # 演示如何在代码中临时切换伪装模式
    original_mode = config.DISGUISE_MODE
    
    print(f"原始伪装模式设置: {original_mode}")
    
    # 临时切换模式
    config.DISGUISE_MODE = not original_mode
    print(f"切换后伪装模式设置: {config.DISGUISE_MODE}")
    
    # 使用新设置创建系统
    system = BacktestSystem(disguise_mode=config.DISGUISE_MODE)
    
    # 恢复原始设置
    config.DISGUISE_MODE = original_mode
    print(f"恢复原始设置: {config.DISGUISE_MODE}")

def compare_modes():
    """
    对比正常模式和伪装模式的效果
    """
    print("\n" + "="*60)
    print("正常模式 vs 伪装模式对比")
    print("="*60)
    
    # 回测参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 1)
    initial_cash = 50000
    
    print("\n1. 正常模式回测:")
    print("-" * 30)
    normal_system = BacktestSystem(disguise_mode=False, enable_cache=True)
    normal_results = normal_system.run_backtest(symbol, start_date, end_date, initial_cash)
    
    print("\n2. 伪装模式回测:")
    print("-" * 30)
    disguise_system = BacktestSystem(disguise_mode=True, enable_cache=True)
    disguise_results = disguise_system.run_backtest(symbol, start_date, end_date, initial_cash)
    
    # 显示图表
    if normal_results and disguise_results:
        print("\n显示对比图表...")
        normal_fig = normal_system.plot_results(symbol)
        disguise_fig = disguise_system.plot_results(symbol)
        
        if normal_fig:
            normal_fig.show()
        if disguise_fig:
            disguise_fig.show()
            
        print("\n对比完成！注意观察两个图表中术语的差异。")

def main():
    """
    主函数
    """
    print("回测系统配置管理演示")
    print("="*70)
    print("可用的演示功能:")
    print("1. run_backtest_with_config() - 使用配置文件运行回测")
    print("2. toggle_disguise_mode() - 演示伪装模式切换")
    print("3. compare_modes() - 对比正常模式和伪装模式")
    print("="*70)
    
    # 运行默认演示
    run_backtest_with_config()
    
    # 可选：运行其他演示
    # toggle_disguise_mode()
    # compare_modes()

if __name__ == "__main__":
    main()
