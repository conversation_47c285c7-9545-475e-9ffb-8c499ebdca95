"""
策略参数传递示例
================

演示如何在 backtrader 中向策略传递自定义参数，
以及如何在策略类中使用这些参数。
"""

import backtrader as bt
from datetime import datetime
from lB_BT_Plotly import BacktestSystem, LongBridgeData
from longport.openapi import Period

class CustomMACDStrategy(bt.Strategy):
    """
    自定义MACD策略 - 演示参数传递
    ============================
    
    这个策略演示如何接收和使用自定义参数
    """
    
    # 策略参数定义
    params = (
        # 原有参数
        ('printlog', True),
        ('fast_period', 12),
        ('slow_period', 26), 
        ('signal_period', 9),
        
        # 新增自定义参数
        ('a', 1.0),           # 自定义参数 a
        ('b', 2.0),           # 自定义参数 b  
        ('c', 3.0),           # 自定义参数 c
        ('risk_level', 0.02), # 风险水平
        ('stop_loss', 0.05),  # 止损比例
        ('take_profit', 0.10), # 止盈比例
    )
    
    def __init__(self):
        """策略初始化"""
        # 计算MACD指标
        self.macd = bt.indicators.MACDHisto(
            self.data.close,
            period_me1=self.params.fast_period,
            period_me2=self.params.slow_period,
            period_signal=self.params.signal_period
        )
        
        # 提取MACD组件
        self.macd_line = self.macd.macd
        self.signal_line = self.macd.signal
        self.histogram = self.macd.histo
        
        # 交叉信号
        self.crossover = bt.indicators.CrossOver(self.macd_line, self.signal_line)
        
        # 初始化状态
        self.order = None
        self.trades = []
        
        # 使用自定义参数
        self.custom_multiplier = self.params.a * self.params.b + self.params.c
        
        # 打印参数信息
        if self.params.printlog:
            self.log(f"策略参数初始化:")
            self.log(f"  a = {self.params.a}")
            self.log(f"  b = {self.params.b}")
            self.log(f"  c = {self.params.c}")
            self.log(f"  计算得到的乘数 = {self.custom_multiplier}")
            self.log(f"  风险水平 = {self.params.risk_level}")
            self.log(f"  止损比例 = {self.params.stop_loss}")
            self.log(f"  止盈比例 = {self.params.take_profit}")
    
    def next(self):
        """策略主逻辑"""
        if self.order:
            return
            
        # 使用自定义参数调整交易逻辑
        current_price = self.data.close[0]
        
        # 买入信号：MACD金叉 且 使用自定义参数调整
        if not self.position and self.crossover > 0:
            # 使用参数 a 调整买入强度
            size_multiplier = self.params.a
            # 计算买入数量（基于风险水平）
            portfolio_value = self.broker.getvalue()
            risk_amount = portfolio_value * self.params.risk_level
            size = int(risk_amount / current_price * size_multiplier)
            
            if size > 0:
                self.log(f'买入信号 - 价格: {current_price:.2f}, 数量: {size}')
                self.log(f'  使用参数 a={self.params.a} 调整买入强度')
                self.order = self.buy(size=size)
        
        # 卖出信号：MACD死叉 或 止盈止损
        elif self.position and (self.crossover < 0 or self._check_exit_conditions()):
            self.log(f'卖出信号 - 价格: {current_price:.2f}')
            self.log(f'  使用参数 b={self.params.b}, c={self.params.c}')
            self.order = self.sell(size=self.position.size)
    
    def _check_exit_conditions(self):
        """检查止盈止损条件"""
        if not self.position:
            return False
            
        current_price = self.data.close[0]
        entry_price = self.position.price
        
        # 计算收益率
        if self.position.size > 0:  # 多头
            return_rate = (current_price - entry_price) / entry_price
        else:  # 空头
            return_rate = (entry_price - current_price) / entry_price
        
        # 使用参数 b 和 c 调整止盈止损
        adjusted_stop_loss = self.params.stop_loss * self.params.b
        adjusted_take_profit = self.params.take_profit * self.params.c
        
        # 止损
        if return_rate <= -adjusted_stop_loss:
            self.log(f'触发止损: {return_rate:.2%} <= -{adjusted_stop_loss:.2%}')
            return True
        
        # 止盈
        if return_rate >= adjusted_take_profit:
            self.log(f'触发止盈: {return_rate:.2%} >= {adjusted_take_profit:.2%}')
            return True
            
        return False
    
    def notify_order(self, order):
        """订单通知"""
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入执行: 价格={order.executed.price:.2f}, '
                        f'数量={order.executed.size}, 手续费={order.executed.comm:.2f}')
            else:
                self.log(f'卖出执行: 价格={order.executed.price:.2f}, '
                        f'数量={order.executed.size}, 手续费={order.executed.comm:.2f}')
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('订单被取消/拒绝')
        
        self.order = None
    
    def notify_trade(self, trade):
        """交易通知"""
        if not trade.isclosed:
            return
        
        self.log(f'交易完成: 毛利润={trade.pnl:.2f}, 净利润={trade.pnlcomm:.2f}')
        self.trades.append({
            'date': self.data.datetime.date(0),
            'pnl': trade.pnl,
            'pnlcomm': trade.pnlcomm
        })
    
    def log(self, txt, dt=None):
        """日志输出"""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')


def demo_strategy_params():
    """
    演示如何传递参数给策略
    """
    print("="*60)
    print("策略参数传递演示")
    print("="*60)
    
    # 创建数据下载器
    data_downloader = LongBridgeData(enable_cache=True)
    
    # 下载数据
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 6, 1)
    
    df = data_downloader.download_data(symbol, start_date, end_date)
    if df is None:
        print("数据下载失败")
        return
    
    # 创建回测引擎
    cerebro = bt.Cerebro()
    
    # 添加数据
    data = bt.feeds.PandasData(dataname=df)
    cerebro.adddata(data)
    
    # 方法1：直接传递参数
    print("\n方法1：直接在 addstrategy 中传递参数")
    cerebro.addstrategy(
        CustomMACDStrategy,
        a=1.5,              # 传递参数 a
        b=2.5,              # 传递参数 b  
        c=3.5,              # 传递参数 c
        risk_level=0.03,    # 风险水平
        stop_loss=0.08,     # 止损比例
        take_profit=0.15,   # 止盈比例
        printlog=True       # 打印日志
    )
    
    # 设置初始资金和手续费
    cerebro.broker.setcash(100000)
    cerebro.broker.setcommission(commission=0.001)
    
    # 运行回测
    print(f"\n开始回测...")
    start_value = cerebro.broker.getvalue()
    results = cerebro.run()
    end_value = cerebro.broker.getvalue()
    
    # 显示结果
    print(f"\n回测结果:")
    print(f"初始资金: ${start_value:,.2f}")
    print(f"最终资金: ${end_value:,.2f}")
    print(f"总收益: ${end_value - start_value:,.2f}")
    print(f"收益率: {((end_value - start_value) / start_value) * 100:.2f}%")


def demo_multiple_strategies():
    """
    演示如何运行多个不同参数的策略进行对比
    """
    print("\n" + "="*60)
    print("多策略参数对比演示")
    print("="*60)
    
    # 参数组合
    param_sets = [
        {'a': 1.0, 'b': 1.0, 'c': 1.0, 'name': '保守策略'},
        {'a': 1.5, 'b': 2.0, 'c': 2.5, 'name': '平衡策略'},
        {'a': 2.0, 'b': 3.0, 'c': 4.0, 'name': '激进策略'},
    ]
    
    results = []
    
    for i, params in enumerate(param_sets):
        print(f"\n运行 {params['name']} (a={params['a']}, b={params['b']}, c={params['c']})")
        
        # 创建独立的回测引擎
        cerebro = bt.Cerebro()
        
        # 重新加载数据
        data_downloader = LongBridgeData(enable_cache=True)
        df = data_downloader.download_data("AAPL.US", datetime(2023, 1, 1), datetime(2023, 6, 1))
        data = bt.feeds.PandasData(dataname=df)
        cerebro.adddata(data)
        
        # 添加策略
        cerebro.addstrategy(
            CustomMACDStrategy,
            a=params['a'],
            b=params['b'],
            c=params['c'],
            printlog=False  # 关闭详细日志
        )
        
        cerebro.broker.setcash(100000)
        cerebro.broker.setcommission(commission=0.001)
        
        # 运行回测
        start_value = cerebro.broker.getvalue()
        cerebro.run()
        end_value = cerebro.broker.getvalue()
        
        # 记录结果
        result = {
            'name': params['name'],
            'params': params,
            'start_value': start_value,
            'end_value': end_value,
            'return': ((end_value - start_value) / start_value) * 100
        }
        results.append(result)
        
        print(f"  收益率: {result['return']:.2f}%")
    
    # 显示对比结果
    print(f"\n策略对比结果:")
    print("-" * 50)
    for result in results:
        print(f"{result['name']:10} | 收益率: {result['return']:6.2f}% | "
              f"参数: a={result['params']['a']}, b={result['params']['b']}, c={result['params']['c']}")


if __name__ == "__main__":
    # 运行演示
    demo_strategy_params()
    
    # 可选：运行多策略对比
    # demo_multiple_strategies()
