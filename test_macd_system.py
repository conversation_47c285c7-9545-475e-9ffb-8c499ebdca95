"""
MACD交易系统测试脚本
===================

用于测试MACD直方图交易系统的各个组件是否正常工作
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 导入系统组件
from macd_trading_config import validate_config, print_config, get_full_config
from enhanced_macd_trading import EnhancedMACDStrategy

def test_config_validation():
    """测试配置验证功能"""
    print("🧪 测试配置验证...")
    
    is_valid, errors = validate_config()
    
    if is_valid:
        print("✅ 配置验证通过")
        return True
    else:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   - {error}")
        return False

def test_macd_calculation():
    """测试MACD计算功能"""
    print("\n🧪 测试MACD计算...")
    
    try:
        # 创建模拟数据
        dates = pd.date_range('2023-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # 生成模拟价格数据
        prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        
        data = pd.DataFrame({
            'open': prices * (1 + np.random.randn(100) * 0.01),
            'high': prices * (1 + np.abs(np.random.randn(100)) * 0.02),
            'low': prices * (1 - np.abs(np.random.randn(100)) * 0.02),
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # 创建策略实例（不连接API）
        config = get_full_config()
        strategy = EnhancedMACDStrategy.__new__(EnhancedMACDStrategy)
        strategy.config = config
        strategy.macd_config = config['macd']
        strategy.filter_config = config['filter']
        strategy.signal_config = config['signal']

        # 设置基本属性
        strategy.histogram_history = []
        strategy.filtered_histogram_history = []
        strategy.diff_history = []

        # 设置logger
        strategy.logger = logging.getLogger('test_strategy')
        strategy.logger.setLevel(logging.ERROR)  # 只显示错误
        
        # 测试MACD计算
        macd_data = strategy.calculate_macd(data)
        
        if not macd_data.empty and len(macd_data) > 0:
            print("✅ MACD计算成功")
            print(f"   数据长度: {len(macd_data)}")
            print(f"   最新MACD值: {macd_data['macd'].iloc[-1]:.6f}")
            print(f"   最新信号值: {macd_data['signal'].iloc[-1]:.6f}")
            print(f"   最新直方图值: {macd_data['histogram'].iloc[-1]:.6f}")
            return True
        else:
            print("❌ MACD计算失败")
            return False
            
    except Exception as e:
        print(f"❌ MACD计算测试失败: {e}")
        return False

def test_filter_function():
    """测试滤波功能"""
    print("\n🧪 测试滤波功能...")
    
    try:
        # 创建策略实例
        config = get_full_config()
        strategy = EnhancedMACDStrategy.__new__(EnhancedMACDStrategy)
        strategy.filter_config = config['filter']
        
        # 创建测试数据
        test_data = [1.0, 2.0, 3.0, 2.5, 1.5, 2.0, 3.5, 4.0, 3.0, 2.0]
        
        # 测试滤波
        filtered_data = strategy.apply_filter(test_data)
        
        if len(filtered_data) == len(test_data):
            print("✅ 滤波功能正常")
            print(f"   原始数据: {test_data[:5]}...")
            print(f"   滤波数据: {[f'{x:.3f}' for x in filtered_data[:5]]}...")
            return True
        else:
            print("❌ 滤波功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 滤波测试失败: {e}")
        return False

def test_signal_generation():
    """测试信号生成功能"""
    print("\n🧪 测试信号生成...")
    
    try:
        # 创建策略实例
        config = get_full_config()
        strategy = EnhancedMACDStrategy.__new__(EnhancedMACDStrategy)
        strategy.signal_config = config['signal']
        strategy.diff_history = []
        
        # 创建测试直方图数据（模拟从负到正的变化）
        strategy.filtered_histogram_history = [
            -0.5, -0.3, -0.1, 0.1, 0.3, 0.5, 0.3, 0.1, -0.1, -0.3
        ]
        
        # 测试信号生成
        signal = strategy.generate_signal()
        
        print(f"✅ 信号生成测试完成")
        print(f"   生成信号: {signal}")
        print(f"   差分历史长度: {len(strategy.diff_history)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号生成测试失败: {e}")
        return False

def test_data_flow():
    """测试完整数据流"""
    print("\n🧪 测试完整数据流...")
    
    try:
        # 创建模拟数据
        dates = pd.date_range('2023-01-01', periods=50, freq='H')
        np.random.seed(42)
        
        prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
        
        data = pd.DataFrame({
            'open': prices * (1 + np.random.randn(50) * 0.01),
            'high': prices * (1 + np.abs(np.random.randn(50)) * 0.02),
            'low': prices * (1 - np.abs(np.random.randn(50)) * 0.02),
            'close': prices,
            'volume': np.random.randint(1000, 10000, 50)
        }, index=dates)
        
        # 创建策略实例
        config = get_full_config()
        strategy = EnhancedMACDStrategy.__new__(EnhancedMACDStrategy)
        strategy.config = config
        strategy.macd_config = config['macd']
        strategy.filter_config = config['filter']
        strategy.signal_config = config['signal']

        # 初始化属性
        strategy.price_data = pd.DataFrame()
        strategy.macd_data = pd.DataFrame()
        strategy.histogram_history = []
        strategy.filtered_histogram_history = []
        strategy.diff_history = []

        # 设置logger
        strategy.logger = logging.getLogger('test_strategy')
        strategy.logger.setLevel(logging.ERROR)  # 只显示错误
        
        # 模拟数据流处理
        strategy.price_data = data
        
        # 计算MACD
        strategy.macd_data = strategy.calculate_macd(strategy.price_data)
        
        if not strategy.macd_data.empty:
            # 更新直方图历史
            histogram_values = strategy.macd_data['histogram'].dropna().tolist()
            strategy.histogram_history = histogram_values
            
            # 滤波
            if len(strategy.histogram_history) >= strategy.filter_config['filter_window']:
                strategy.filtered_histogram_history = strategy.apply_filter(strategy.histogram_history)
                
                # 生成信号
                signal = strategy.generate_signal()
                
                print("✅ 完整数据流测试成功")
                print(f"   价格数据长度: {len(strategy.price_data)}")
                print(f"   MACD数据长度: {len(strategy.macd_data)}")
                print(f"   直方图历史长度: {len(strategy.histogram_history)}")
                print(f"   滤波历史长度: {len(strategy.filtered_histogram_history)}")
                print(f"   生成信号: {signal}")
                
                return True
            else:
                print("⚠️ 数据不足，无法完成完整流程")
                return False
        else:
            print("❌ MACD计算失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整数据流测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接（可选）"""
    print("\n🧪 测试API连接...")
    
    try:
        from longport.openapi import Config, QuoteContext
        
        # 尝试创建连接
        config = Config.from_env()
        ctx = QuoteContext(config)
        
        print("✅ Longbridge API连接成功")
        print("   注意: 这只是连接测试，未进行实际数据请求")
        return True
        
    except Exception as e:
        print(f"⚠️ API连接测试失败: {e}")
        print("   这可能是因为未设置环境变量或网络问题")
        print("   如果不进行实盘交易，可以忽略此错误")
        return False

def run_all_tests():
    """运行所有测试"""
    print("="*60)
    print("🧪 MACD交易系统测试套件")
    print("="*60)
    
    tests = [
        ("配置验证", test_config_validation),
        ("MACD计算", test_macd_calculation),
        ("滤波功能", test_filter_function),
        ("信号生成", test_signal_generation),
        ("完整数据流", test_data_flow),
        ("API连接", test_api_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-"*60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
    elif passed >= total * 0.8:
        print("⚠️ 大部分测试通过，系统基本可用。")
    else:
        print("❌ 多个测试失败，请检查系统配置。")
    
    print("="*60)

if __name__ == "__main__":
    # 设置日志级别，避免测试时的日志干扰
    logging.getLogger().setLevel(logging.ERROR)
    
    run_all_tests()
